---
title: Introduction
description: Welcome to the Text-to-Speech (TTS) API, your tool for turning text, documents, and subtitles into lifelike speech.
---

The Text To Speech OpenAI API is a robust and easy-to-use service designed to convert written content into high-quality, natural-sounding speech. This API caters to a wide range of use cases, such as creating voiceovers for multimedia content, generating narrations for e-books and documents, or turning subtitles into engaging audio experiences.

## Features

- **Multi-Language Support** - Generate speech in various languages with diverse voice options, including male and female tones.
- **Customizable Audio Settings** - Adjust speech speed, pitch, and output formats to match your needs.
- **Document and Subtitle Handling** - Seamlessly convert .txt, .docx, .pdf, or .srt files into audio.
- **Storytelling Capabilities** - Transform text or subtitle files into captivating narrated stories.

With simple integration and high scalability, the TTS OpenAI API is the ideal solution for developers and businesses seeking to enhance accessibility, automate voice creation, or elevate user experiences.

## Check our services

You can try 

::u-button
---
class: mr-4
icon: 'i-ion-language'
label: Try with Text
target: _blank
to: https://ttsopenai.com/
---
::

::u-button
---
class: mt-2 sm:mt-0
icon: 'i-gg-file-document'
label: Try with document
target: _blank
to: https://ttsopenai.com/documents
---
::
