---
title: Custom Vibes
description: Create and manage custom emotional vibes for personalized speech generation.
---

Custom Vibes allow you to create, manage, and use personalized emotional expressions for your text-to-speech conversions. This feature enables you to define specific emotional contexts and prompts that can be reused across multiple speech generation requests, providing consistent and tailored emotional expression for your applications.

## Overview

The Custom Vibes API provides comprehensive functionality to:
- Create custom emotional vibes with specific prompts
- Retrieve and manage your personal vibe library
- Access master vibes provided by the platform
- Search and find specific vibes
- Update and delete existing custom vibes

## Get User Custom Vibes
`GET https://api.ttsopenai.com/uapi/v1/custom-vibes`

Retrieve all custom vibes created by the authenticated user.

### Example Request
::code-group
```bash [terminal]
curl -X GET https://api.ttsopenai.com/uapi/v1/custom-vibes \
  -H "x-api-key: <your api key>"
```

```python [python]
import requests

url = "https://api.ttsopenai.com/uapi/v1/custom-vibes"
headers = {
    "x-api-key": "<your api key>"
}

response = requests.get(url, headers=headers)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.ttsopenai.com/uapi/v1/custom-vibes";
const headers = {
    "x-api-key": "<your api key>"
};

axios.get(url, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Example Response
```json [Response]
{
  "success": true,
  "result": [
    {
      "id": 1,
      "vibe": "Professional Presenter",
      "prompt": "Speak with confidence and authority, as if presenting to a business audience",
      "created_at": "2024-11-21T10:30:00",
      "updated_at": "2024-11-21T10:30:00"
    },
    {
      "id": 2,
      "vibe": "Storyteller",
      "prompt": "Use a warm, engaging tone with dramatic pauses and expressive inflection",
      "created_at": "2024-11-21T11:15:00",
      "updated_at": "2024-11-21T11:15:00"
    }
  ],
  "message": "Custom vibes retrieved successfully"
}
```

## Create Custom Vibe
`POST https://api.ttsopenai.com/uapi/v1/custom-vibes`

Create a new custom vibe with a specific emotional context and prompt.

### Example Request
::code-group
```bash [terminal]
curl -X POST https://api.ttsopenai.com/uapi/v1/custom-vibes \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
    "vibe": "Motivational Coach",
    "prompt": "Speak with high energy, enthusiasm, and encouragement to inspire action"
  }'
```

```python [python]
import requests

url = "https://api.ttsopenai.com/uapi/v1/custom-vibes"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
    "vibe": "Motivational Coach",
    "prompt": "Speak with high energy, enthusiasm, and encouragement to inspire action"
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.ttsopenai.com/uapi/v1/custom-vibes";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};
const data = {
    "vibe": "Motivational Coach",
    "prompt": "Speak with high energy, enthusiasm, and encouragement to inspire action"
};

axios.post(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Request Attributes

`vibe` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name or title of the custom vibe. This should be descriptive and help you identify the emotional context.

`prompt` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The detailed prompt that describes how the emotion should be expressed. This guides the AI in generating speech with the desired emotional characteristics.

### Example Response
```json [Response]
{
  "success": true,
  "result": {
    "id": 3,
    "vibe": "Motivational Coach",
    "prompt": "Speak with high energy, enthusiasm, and encouragement to inspire action",
    "created_at": "2024-11-21T12:00:00",
    "updated_at": "2024-11-21T12:00:00"
  },
  "message": "Custom vibe created successfully"
}
```

## Get Master Vibes
`GET https://api.ttsopenai.com/uapi/v1/master-vibes`

Retrieve all master vibes provided by the platform. These are pre-defined emotional contexts that you can use in your speech generation.

### Example Request
::code-group
```bash [terminal]
curl -X GET https://api.ttsopenai.com/uapi/v1/master-vibes \
  -H "x-api-key: <your api key>"
```

```python [python]
import requests

url = "https://api.ttsopenai.com/uapi/v1/master-vibes"
headers = {
    "x-api-key": "<your api key>"
}

response = requests.get(url, headers=headers)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.ttsopenai.com/uapi/v1/master-vibes";
const headers = {
    "x-api-key": "<your api key>"
};

axios.get(url, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Example Response
```json [Response]
{
  "success": true,
  "result": [
    {
      "id": 1,
      "vibe": "Happy",
      "prompt": "Express joy and positivity with an upbeat tone"
    },
    {
      "id": 2,
      "vibe": "Calm",
      "prompt": "Speak in a peaceful, relaxed manner with steady pacing"
    },
    {
      "id": 3,
      "vibe": "Excited",
      "prompt": "Show enthusiasm and energy with animated expression"
    }
  ],
  "message": "Master vibes retrieved successfully"
}
```

## Update Custom Vibe
`PUT https://api.ttsopenai.com/uapi/v1/custom-vibes/{id}`

Update an existing custom vibe by its ID.

### Example Request
::code-group
```bash [terminal]
curl -X PUT https://api.ttsopenai.com/uapi/v1/custom-vibes/3 \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
    "vibe": "Inspirational Speaker",
    "prompt": "Deliver with passion and conviction, inspiring listeners to take action"
  }'
```

```python [python]
import requests

url = "https://api.ttsopenai.com/uapi/v1/custom-vibes/3"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
    "vibe": "Inspirational Speaker",
    "prompt": "Deliver with passion and conviction, inspiring listeners to take action"
}

response = requests.put(url, headers=headers, json=data)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.ttsopenai.com/uapi/v1/custom-vibes/3";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};
const data = {
    "vibe": "Inspirational Speaker",
    "prompt": "Deliver with passion and conviction, inspiring listeners to take action"
};

axios.put(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Request Attributes

`vibe` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The updated name or title of the custom vibe.

`prompt` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The updated prompt that describes how the emotion should be expressed.

### Example Response
```json [Response]
{
  "success": true,
  "result": {
    "id": 3,
    "vibe": "Inspirational Speaker",
    "prompt": "Deliver with passion and conviction, inspiring listeners to take action",
    "created_at": "2024-11-21T12:00:00",
    "updated_at": "2024-11-21T12:30:00"
  },
  "message": "Custom vibe updated successfully"
}
```

## Delete Custom Vibe
`DELETE https://api.ttsopenai.com/uapi/v1/custom-vibes/{id}`

Delete a custom vibe by its ID.

### Example Request
::code-group
```bash [terminal]
curl -X DELETE https://api.ttsopenai.com/uapi/v1/custom-vibes/3 \
  -H "x-api-key: <your api key>"
```

```python [python]
import requests

url = "https://api.ttsopenai.com/uapi/v1/custom-vibes/3"
headers = {
    "x-api-key": "<your api key>"
}

response = requests.delete(url, headers=headers)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.ttsopenai.com/uapi/v1/custom-vibes/3";
const headers = {
    "x-api-key": "<your api key>"
};

axios.delete(url, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Path Parameters

`id` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The unique identifier of the custom vibe to delete.

### Example Response
```json [Response]
{
  "success": true,
  "result": "Custom vibe deleted successfully",
  "message": "Custom vibe with ID 3 has been removed"
}
```

## Search Custom Vibes
`GET https://api.ttsopenai.com/uapi/v1/custom-vibes/search`

Search for custom vibes by keyword.

### Example Request
::code-group
```bash [terminal]
curl -X GET "https://api.ttsopenai.com/uapi/v1/custom-vibes/search?key=motivational" \
  -H "x-api-key: <your api key>"
```

```python [python]
import requests

url = "https://api.ttsopenai.com/uapi/v1/custom-vibes/search"
headers = {
    "x-api-key": "<your api key>"
}
params = {
    "key": "motivational"
}

response = requests.get(url, headers=headers, params=params)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.ttsopenai.com/uapi/v1/custom-vibes/search";
const headers = {
    "x-api-key": "<your api key>"
};
const params = {
    key: "motivational"
};

axios.get(url, { headers, params })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Query Parameters

`key` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The search keyword to find matching vibes. The search will look for matches in both vibe names and prompts.

### Example Response
```json [Response]
{
  "success": true,
  "result": [
    {
      "id": 1,
      "vibe": "Motivational Coach",
      "prompt": "Speak with high energy, enthusiasm, and encouragement to inspire action",
      "created_at": "2024-11-21T12:00:00",
      "updated_at": "2024-11-21T12:00:00"
    }
  ],
  "message": "Search completed successfully"
}
```

## Using Custom Vibes in Speech Generation

Once you have created custom vibes, you can use them in your emotion-to-speech requests by referencing the vibe ID:

```json
{
  "model": "audio_stable",
  "voice_id": "OA001",
  "speed": 1,
  "input": "Welcome to our presentation today!",
  "vibe_id": 1,
  "emotion": "professional",
  "custom_prompt": "Speak with confidence and authority, as if presenting to a business audience"
}
```

## Best Practices

### Creating Effective Vibes
- **Be Specific**: Use detailed prompts that clearly describe the desired emotional expression
- **Use Context**: Include situational context in your prompts (e.g., "as if speaking to children", "like a news anchor")
- **Test and Iterate**: Create test audio with your vibes and refine the prompts based on results

### Organizing Your Vibes
- **Descriptive Names**: Use clear, descriptive names that make it easy to identify the right vibe
- **Categorize**: Consider creating vibes for different use cases (presentations, storytelling, customer service, etc.)
- **Regular Cleanup**: Remove unused vibes to keep your library organized

### Prompt Writing Tips
- **Tone Descriptors**: Use words like "warm", "authoritative", "gentle", "energetic"
- **Pacing Instructions**: Include guidance on speed and rhythm ("with dramatic pauses", "at a steady pace")
- **Emotional Context**: Specify the underlying emotion and intensity level
- **Audience Consideration**: Mention the intended audience when relevant
