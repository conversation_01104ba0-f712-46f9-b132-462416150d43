---
title: Emotion
description: Generate expressive speech with emotional context and custom vibes.
---

The Emotion To Speech API allows you to convert text into high-quality, emotionally expressive speech. This API extends the basic text-to-speech functionality by adding emotional context through vibe settings and custom prompts, enabling you to create more engaging and contextually appropriate audio content.

## Emotion To Speech
`POST https://api.ttsopenai.com/uapi/v1/text-to-speech-advanced`

This endpoint allows you to convert text into speech with emotional expression. You can customize the voice, speed, model, emotional vibe, and provide custom prompts for enhanced expressiveness.


### Example Request
::code-group
```bash [terminal]
curl -X POST https://api.ttsopenai.com/uapi/v1/text-to-speech-advanced \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
    "model": "audio_stable",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello, my name is <PERSON><PERSON><PERSON>. I am excited to help you today!",
    "vibe_id": 1,
    "emotion": "excited",
    "custom_prompt": "Speak with enthusiasm and energy"
  }'
```

```ts [py]
import requests

url = "https://api.ttsopenai.com/uapi/v1/text-to-speech-advanced"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
    "model": "audio_stable",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello world! I'm feeling great today!",
    "vibe_id": 2,
    "emotion": "happy",
    "custom_prompt": "Express joy and positivity"
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

```ts [ts]
import axios from 'axios';

const url = "https://api.ttsopenai.com/uapi/v1/text-to-speech-advanced";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};
const data = {
    "model": "audio_stable",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello world! This is amazing!",
    "vibe_id": 3,
    "emotion": "amazed",
    "custom_prompt": "Show wonder and amazement"
};

axios.post(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Request Attributes

`model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model used for the conversion. Fixed value: `audio_stable`.

`voice_id` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The voice used for the conversion. You can find the list of voice IDs in the [Voice Library](https://ttsopenai.com/voice-library). The default value is `OA001`.

`speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of the speech. The value should be between 1 and 4. The default value is 1.

`input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The text to be converted into speech. The maximum length is 10,000 characters.

`vibe_id` [number]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The emotional vibe identifier used to control the emotional expression of the speech. This numeric value corresponds to predefined emotional settings.

`emotion` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The specific emotion to be expressed in the speech. Examples include "happy", "sad", "excited", "calm", "angry", "surprised", etc.

`custom_prompt` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

A custom prompt that provides additional context or instructions for how the emotion should be expressed in the speech generation.

### Example Response
```json [Response]
{
  "success": true,
  "result": {
    "uuid": "eef94c08-a806-11ef-b617-22023a24db09",
    "voice_id": "OA001",
    "speed": 1,
    "model": "audio_stable",
    "tts_input": "Hello, my name is OpenAI. I am excited to help you today!",
    "vibe_id": 1,
    "emotion": "excited",
    "custom_prompt": "Speak with enthusiasm and energy",
    "estimated_credit": 58,
    "used_credit": 58,
    "status": 1,
    "status_percentage": 50,
    "error_message": "",
    "speaker_name": "Alloy",
    "created_at": "2024-11-21T12:48:40",
    "updated_at": "2024-11-21T12:48:40"
  }
}
```

### Response Attributes

`success` [boolean]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Indicates whether the request was successful.

`result` [object]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The result of the emotion-to-speech conversion.

`result.uuid` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The unique identifier for the conversion.

`result.voice_id` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The voice used for the conversion.

`result.speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of the speech.

`result.model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model used for the conversion. Fixed value: `audio_stable`.

`result.tts_input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The text that was converted into speech.

`result.vibe_id` [number]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The emotional vibe identifier used for the conversion.

`result.emotion` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The specific emotion expressed in the speech.

`result.custom_prompt` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The custom prompt used for emotional expression guidance.

`result.estimated_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The estimated number of credits used for the conversion.

`result.used_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The actual number of credits used for the conversion.

`result.status` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The status of the conversion. Possible values are:

- `1`: Converting
- `2`: Completed
- `3`: Error
- `11`: Reworking
- `12`: Joining Audio
- `13`: Merging Audio
- `14`: Downloading Audio

`result.status_percentage` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The percentage of the conversion that has been completed.

`result.error_message` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The error message, if any.

`result.speaker_name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name of the speaker.

`result.created_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the conversion was created.

`result.updated_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the conversion was last updated.
