---
title: Text
description: Generate lifelike speech from text in multiple languages and voices.
---

The Text To Speech OpenAI (TTS) API allows you to convert text into high-quality, natural-sounding speech. You can use this API to generate voiceovers for multimedia content, create narrations for e-books and documents, or turn subtitles into engaging audio experiences.

## Text To Speech
`POST https://api.ttsopenai.com/uapi/v1/text-to-speech`

This endpoint allows you to convert text into speech. You can customize the voice, speed, and model used for the conversion.


### Example Request
::code-group
```bash [terminal]
curl -X POST https://api.ttsopenai.com/uapi/v1/text-to-speech \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
    "model": "tts-1",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello, my name is <PERSON>A<PERSON>. I am a text-to-speech model."
  }'
```

```ts [py]
import requests

url = "https://api.ttsopenai.com/uapi/v1/text-to-speech"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
    "model": "tts-1",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello world!"
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

```ts [ts]
import axios from 'axios';

const url = "https://api.ttsopenai.com/uapi/v1/text-to-speech";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};
const data = {
    "model": "tts-1",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello world!"
};

axios.post(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Request Attributes
<!-- model	string	chỉ có thể chọn tts-1 hoặc tts-1-hd, mặc định là tts-1	
voice_id	string	tham khảo danh sách voice id ở sheet bên cạnh, mặc định là OA001	
speed	float	từ 1-4, mặc định là 1	
input*	string	max 10000 chars	 -->
<!-- space -->

`model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model used for the conversion. You can choose between `tts-1` and `tts-1-hd`. The default value is `tts-1`.

`voice_id` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The voice used for the conversion. You can find the list of voice IDs in the [Voice Library](https://ttsopenai.com/voice-library). The default value is `OA001`.

`speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of the speech. The value should be between 1 and 4. The default value is 1.

`input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The text to be converted into speech. The maximum length is 10,000 characters.

### Example Response
```json [Response]
{
  "success": true,
  "result": {
    "uuid": "eef94c08-a806-11ef-b617-22023a24db09",
    "voice_id": "OA001",
    "speed": 1,
    "model": "tts-1",
    "tts_input": "Hello, my name is OpenAI. I am a text-to-speech model.",
    "estimated_credit": 54,
    "used_credit": 54,
    "status": 1,
    "status_percentage": 50,
    "error_message": "",
    "speaker_name": "Alloy",
    "created_at": "2024-11-21T12:48:40",
    "updated_at": "2024-11-21T12:48:40"
  }
}
```

### Response Attributes

`success` [boolean]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Indicates whether the request was successful.

`result` [object]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The result of the text-to-speech conversion.

`result.uuid` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The unique identifier for the conversion.

`result.voice_id` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The voice used for the conversion.

`result.speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of the speech.

`result.model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model used for the conversion.

`result.tts_input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The text that was converted into speech.

`result.estimated_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The estimated number of credits used for the conversion.

`result.used_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The actual number of credits used for the conversion.

`result.status` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The status of the conversion. Possible values are:

<!-- CONVERTING = 1
JOINING_AUDIO = 12
MERGING_AUDIO = 13
DOWNLOADING_AUDIO = 14
REWORKING = 11
COMPLETED = 2
ERROR = 3 -->

- `1`: Converting
- `2`: Completed
- `3`: Error
- `11`: Reworking
- `12`: Joining Audio
- `13`: Merging Audio
- `14`: Downloading Audio

`result.status_percentage` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The percentage of the conversion that has been completed.

`result.error_message` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The error message, if any.

`result.speaker_name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name of the speaker.

`result.created_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the conversion was created.

`result.updated_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the conversion was last updated.
