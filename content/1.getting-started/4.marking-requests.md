---
title: Making requests
description: Make your first request to the Text-to-Speech (TTS) API and get started with the powerful features.
---

After setting up your account and obtaining your API key, setup webhooks, you can start making requests to the Text To Speech OpenAI (TTS) API. This guide will walk you through the process of making your first request and provide examples of how to use the API to generate speech from text.

::code-group

```bash [terminal]
curl -X POST https://api.ttsopenai.com/uapi/v1/text-to-speech \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
    "model": "tts-1",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello, my name is <PERSON><PERSON><PERSON>. I am a text-to-speech model."
  }' \
```

```ts [py]
import requests

url = "https://api.ttsopenai.com/uapi/v1/text-to-speech"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
    "model": "tts-1",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello world!"
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

```ts [ts]
import axios from 'axios';

const url = "https://api.ttsopenai.com/uapi/v1/text-to-speech";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};
const data = {
    "model": "tts-1",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello world!"
};

axios.post(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```

::

:::callout
---
icon: i-heroicons-light-bulb
target: _blank
to: https://ttsopenai.com/voice-library
---
You can find the voice IDs in Voice Library page.
