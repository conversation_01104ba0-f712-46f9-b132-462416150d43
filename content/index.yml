title: 'Text To Speech OpenAI API'
description: Build a Text To Speech app with TTS OpenAI API.
navigation: false
hero:
  title: 'Build your Text To Speech App in seconds'
  description: 'Use the TTSOpenAI API to generate human-like speech from text.'
  orientation: horizontal
  links:
    - label: Get started
      icon: i-heroicons-arrow-right-20-solid
      trailing: true
      to: '/getting-started'
      size: lg
    - label: Try our service
      icon: i-mingcute-voice-line
      size: lg
      color: gray
      to: https://ttsopenai.com/
      target: _blank
  code: |
    ```bash [terminal]
    curl -X POST https://api.ttsopenai.com/uapi/v1/text-to-speech \
      -H "Content-Type: application/json" \
      -H "x-api-key: <your api key>" \
      -d '{ \
            "model":"tts-1", \
            "voice_id":"OA001", \
            "speed": 1, \
            "input": "Hello world!" \
          }'
    ```
features:
  title: 'A powerful Text To Speech API'
  links:
    - label: 'Explore Text To Speech Service'
      icon: 'i-mingcute-voice-line'
      trailingIcon: 'i-heroicons-arrow-right-20-solid'
      color: 'gray'
      to: 'https://ttsopenai.com/'
      target: '_blank'
      size: lg
  items:
    - title: 'Text'
      description: 'Convert text to speech.'
      icon: 'i-ion-language'
      to: '/resources/tts-text'
      target: '_blank'
    - title: 'Document'
      description: 'Convert document to speech.'
      icon: 'i-gg-file-document'
      to: '/resources/tts-document'
      target: '_blank'
    - title: 'Story Maker'
      description: 'Create a story with multiple voices.'
      icon: 'i-fluent-people-chat-16-regular'
      to: '/resources/tts-story'
      target: '_blank'
    - title: 'Secure'
      description: 'Secure your API with API Key.'
      icon: 'i-iconoir-secure-window'
      to: '/getting-started/authentication'
      target: '_blank'
    - title: 'Webhook'
      description: 'Receive the result via webhook.'
      icon: 'i-ic-baseline-webhook'
      to: '/getting-started/webhooks'
      target: '_blank'
    - title: 'Languages'
      description: 'Support multiple languages.'
      icon: 'i-material-symbols-language'
