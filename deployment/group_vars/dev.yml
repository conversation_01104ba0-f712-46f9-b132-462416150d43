---
## Require variables
s3_bucket_name: "tts-openai-dev-docs-frontend-bucket"
cloudfront_distribution_id: ""
application_name: "docs-frontend"
parameter: "cdk-tts-openai-docs-frontend-dev"

## Optional variables
application_path: "{{ playbook_dir }}"
env: "dev"
image_location: "local"
image_full_name: "{{ image_location }}/{{application_name}}-{{env}}-service"
output_folder:
  name: ".output"
  source: "public"
build_docker: true
aws_region: "eu-central-1"
docker_target: "build"
