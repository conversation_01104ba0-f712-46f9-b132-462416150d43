{"name": "nuxt-ui-pro-template-docs", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "typecheck": "nuxt typecheck"}, "dependencies": {"@iconify-json/heroicons": "^1.2.1", "@iconify-json/simple-icons": "^1.2.9", "@nuxt/content": "^2.13.4", "@nuxt/fonts": "^0.10.0", "@nuxt/image": "^1.8.1", "@nuxt/ui-pro": "^1.4.4", "nuxt": "^3.13.2", "nuxt-og-image": "^3.0.6"}, "devDependencies": {"@nuxt/eslint": "^0.6.0", "@nuxthq/studio": "^2.1.1", "eslint": "^9.13.0", "vue-tsc": "^2.1.6"}, "packageManager": "pnpm@9.12.2"}